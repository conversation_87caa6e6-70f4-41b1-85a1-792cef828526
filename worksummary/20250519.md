# 工作周报

<table><tr><td>时间</td><td>工作事项</td><td>备注</td></tr><tr><td>周一</td><td>在 LibAFL 框架上实现 mopt fuzzer，主要基于 LibAFL 的 StdMOptMutator 组件，复现 MOPT 论文中的自适应变异概率调整算法 fuzzer</td><td></td></tr><tr><td>周二</td><td>在 LibAFL 上实现 AFLFast 的 power scheduling 策略，使稀有路径获得更多能量分配，主要基于 AFL++ 的能量调度模块实现</td><td></td></tr><tr><td>周三</td><td>阅读 CCS&#x27;24 论文《On Understanding and Forecasting Fuzzers Performance with Static Analysis》（非常相关的工作），这篇文章评估不同策略组合的干扰程度（同样基于 LibAFL），预测最优组合以最大化分支覆盖率。</td><td></td></tr><tr><td>周四</td><td>复现 CCS&#x27;24 论文中的实验，详细阅读其基于 LibAFL 实现的多种 fuzzer 源码，与贾老师讨论与该工作在方案设计上的不同</td><td></td></tr><tr><td>周五</td><td>参考论文设计了 baseline 实验，在 fuzzbench 上搭建初步实验环境，完成目标程序插桩编译和 Fuzz 配置</td><td></td></tr><tr><td>周六</td><td>法定节假日</td><td></td></tr><tr><td>周日</td><td>法定节假日</td><td></td></tr></table>

时间：2025年5月12日—2025年5月18日  $⑨$  专项工作时间：5天其他项目工作时间：0天

本人签字： 审核人签字：