# 工作周报

<table><tr><td>时间</td><td>工作事项</td><td>备注</td></tr><tr><td>周一</td><td>法定节假日</td><td></td></tr><tr><td>周二</td><td>审稿 ccs25 论文《The Hidden Threat in Plain Text: Attacking RAG Data Loaders》</td><td></td></tr><tr><td>周三</td><td>完善 FuzzLM-Agent 的分析服务架构，实现统一分析引擎和安全模式检测器，将 litellm 的接口调用重构为异步分析处理和并发控制机制</td><td></td></tr><tr><td>周四</td><td>实现监控服务模块，包括实时状态监控、性能指标聚合，这一部分将作为状态结合策略让 Fuzzlm-agent 进行反思和 fuzz 的调整</td><td></td></tr><tr><td>周五</td><td>继续调试状态监控模块，集成 LibAFL 代码生成器，基于分析结果自动生成 Rust 测试代码</td><td></td></tr><tr><td>周六</td><td>法定节假日</td><td></td></tr><tr><td>周日</td><td>法定节假日</td><td></td></tr></table>

时间：2025年6月2日—2025年6月8日  $⑨$  专项工作时间：4天其他项目工作时间：0天

本人签字： 审核人签字：