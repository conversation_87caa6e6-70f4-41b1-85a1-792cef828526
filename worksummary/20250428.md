# 工作周报

<table><tr><td>时间</td><td>工作事项</td><td>备注</td></tr><tr><td>周一</td><td>针对agent使用KLEE时遇到的问题进行调试，主要解决了LLVM版本依赖冲突和动态链接库缺失问题</td><td></td></tr><tr><td>周二</td><td>添加coreutils测试用例分析流程作为agent的knowledge（基于KLEE官方tutorial）</td><td></td></tr><tr><td>周三</td><td>阅读并调试Facebook的DocAgent开源项目代码，重点分析其基于reader/searcher/writer以及verifier的workflow</td><td></td></tr><tr><td>周四</td><td>将DocAgent功能模块与agent进行集成，目标是使agent能够利用文档知识辅助目标程序的安全分析</td><td></td></tr><tr><td>周五</td><td>对DocAgent的docstring查找生成流程进行优化，主要实现了并发处理机制，对没有依赖关系的节点进行并行化处理</td><td></td></tr><tr><td>周六</td><td>法定节假日</td><td></td></tr><tr><td>周日</td><td>继续添加docagent的并发处理实现以及在OpenHands上的集成</td><td></td></tr></table>

时间：2025年4月21日—2025年4月27日  $⑨$  专项工作时间：6天其他项目工作时间：0天

本人签字： 审核人签字：