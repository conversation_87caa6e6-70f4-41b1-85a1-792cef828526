# 工作周报

<table><tr><td>时间</td><td>工作事项</td><td>备注</td></tr><tr><td>周一</td><td>处理了配置系统重构后的测试兼容性问题，整理了测试目录结构；修复了mock服务和真实gRPC服务间的切换逻辑</td><td></td></tr><tr><td>周二</td><td>阅读论文《ChainFuzz: Exploiting Upstream Vulnerabilities in Open-Source Supply Chains》；优化了Python-Rust间的数据传输效率，通过protobuf消息结构重设计和批量处理机制。实现异步消息队列缓冲，改进gRPC连接池管理</td><td></td></tr><tr><td>周三</td><td>重构了多维向量检索核心算法，优化经验知识的特征提取和相似度计算，提升模糊测试经验匹配准确率。添加了增量学习机制，可以动态更新知识库而无需重建索引</td><td></td></tr><tr><td>周四</td><td>实现了LibAFL组件的语义分析功能，LLM现在能根据目标程序特征精准选择mutator组合和feedback机制。策略新增自适应参数调优，提升fuzzer配置的有效性和针对性</td><td></td></tr><tr><td>周五</td><td>添加了实时状态数据流分析模块，可以跟踪覆盖率增长速度、crash发现效率、资源使用情况等关键指标</td><td></td></tr><tr><td>周六</td><td>法定节假日</td><td></td></tr><tr><td>周日</td><td>法定节假日</td><td></td></tr></table>

时间：2025年6月9日—2025年6月15日  $⑨$  专项工作时间：5天其他项目工作时间：0天

本人签字： 审核人签字：