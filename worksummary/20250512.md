# 工作周报

<table><tr><td>时间</td><td>工作事项</td><td>备注</td></tr><tr><td>周一</td><td>法定节假日</td><td></td></tr><tr><td>周二</td><td>围绕基于程序特征的 Fuzz 策略推荐系统、资源分配框架等创新点，继续调研 LibAFL、InteFuzz、EnFuzz 等主流模糊测试框架与最新研究，总结现有方法的优缺点</td><td></td></tr><tr><td>周三</td><td>整理调研结果，与贾老师讨论明确了论文目标和系统核心功能，初步搭建整体框架设计文档</td><td></td></tr><tr><td>周四</td><td>基于 LibAFL 完成项目基础结构的搭建，完成 LLM 集成模块和 prompt 模块</td><td></td></tr><tr><td>周五</td><td>继续完善系统基础架构，开发分析系统，实现对源代码静态分析，支持多种程序特征的表示和处理</td><td></td></tr><tr><td>周六</td><td>法定节假日</td><td></td></tr><tr><td>周日</td><td>法定节假日</td><td></td></tr></table>

时间：2025年5月5日—2025年5月11日  $⑨$  专项工作时间：4天其他项目工作时间：0天

本人签字： 审核人签字：