# 工作周报

<table><tr><td>时间</td><td>工作事项</td><td>备注</td></tr><tr><td>周一</td><td>审稿论文《组件级分析的算法复杂性漏洞检测方法研究》</td><td></td></tr><tr><td>周二</td><td>撰写“PyPI生态漏洞细粒度评估分析系统”的程序说明书和整理源代码，并提交申请软著</td><td></td></tr><tr><td>周三</td><td>修改Camera-Ready论文，重点根据反馈意见更新错误和补充内容</td><td></td></tr><tr><td>周四</td><td>继续完善论文的修订稿，主要是修改语法和表述错误，调整全文格式，确保符合会议提交要求后提交</td><td></td></tr><tr><td>周五</td><td>实现论文FuzzLM-Agent中LLM Agent核心（Python实现）与LibAFL模糊测试执行模块（Rust实现）之间的基础进程间通信，并初步验证了策略指令发送与状态数据接收的通路</td><td></td></tr><tr><td>周六</td><td>法定节假日</td><td></td></tr><tr><td>周日</td><td>法定节假日</td><td></td></tr></table>

时间：2025年5月19日—2025年5月25日  $⑨$  专项工作时间：5天其他项目工作时间：0天

本人签字： 审核人签字：